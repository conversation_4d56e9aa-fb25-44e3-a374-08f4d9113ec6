export const FAMILY_GIVEN = 'familygiven',
	GIVEN_FAMILY = 'givenfamily',
	UNDEFINED = 'und',
	CHINESE = 'cmn',
	JAPANESE = 'jpn',
	KOREAN = 'kor',
	NON_ENGLISH = [ CHINESE, JAPANESE, KOREAN ],
	CN_JP_KR_REGEX = /^[\u4e00-\u9fa5\uac00-\ud7ff\u0800-\u4e00]+$/, // Chinese, Japanese, Korean
	SEPARATORS = [
		['-', 'zh', 'ch', 'sh', 'hs', 'ts', 'sw', 'sz', 'tz'],
		['w', 'x', 'y', 'z', 'b', 'p', 'f', 'd', 'l', 'j', 'q', 'c', 's'],
		['m', 'k', 't', 'h'],
		['r', 'n', 'g'],
	],
	SPECIAL_NAMES: Record<string, { ethnicity: number, gender: 1 | 2 }> = {
		// bin: { ethnicity: 108, gender: 1, bias: { ethnicity: 50 } },
		ali: { ethnicity: 108, gender: 1 },
		muhammad: { ethnicity: 108, gender: 1 },
		mohammad: { ethnicity: 108, gender: 1 },
		mohammed: { ethnicity: 108, gender: 1 },
		muhd: { ethnicity: 108, gender: 1 },
		mohd: { ethnicity: 108, gender: 1 },
		mamat: { ethnicity: 108, gender: 1 },
		ahmad: { ethnicity: 108, gender: 1 },
		abdul: { ethnicity: 108, gender: 1 },
		binte: { ethnicity: 108, gender: 2 },
		binti: { ethnicity: 108, gender: 2 },
		bte: { ethnicity: 108, gender: 2 },
		bt: { ethnicity: 108, gender: 2 },
		nur: { ethnicity: 108, gender: 2 },
		nurul: { ethnicity: 108, gender: 2 },
		noor: { ethnicity: 108, gender: 2 },
		nor: { ethnicity: 108, gender: 2 },
		siti: { ethnicity: 108, gender: 2 },
	},
	LANGUAGE_BIAS: Record<string, Record<number, number>> = {
		cmn: { 101: 80 },
		eng: {},
		jpn: { 104: 80 },
		kor: { 105: 80 },
		vie: { 110: 80 },
		mal: { 108: 80 },
		ind: { 103: 80 },
		fra: { 201: 80 }, // French
		tha: { 109: 80 },
		deu: { 201: 80 }, // German
		spa: { 107: 50, 201: 50 }, // Spanish
		// in: { 102: 80 }, // language?
		por: { 107: 50, 201: 50 }, // Portuguese
		rus: { 201: 80 }, // Russian
		ita: { 201: 80 }, // Italian
		arb: { 501: 80 }, // Arabic
		nld: { 201: 80 }, // Dutch
		swe: { 201: 80 }, // Swedish
	},
	COUNTRY_BIAS: Record<string, Record<number, number>> = {
		SG: { 101: 35, 108: 10, 102: 5 },
		TW: { 101: 50 },
		HK: { 101: 50 },
		CN: { 101: 60 },
		MY: { 108: 60, 101: 20 },
		JP: { 104: 60 },
		MO: { 101: 50 },
		PH: { 107: 60 },
		US: { 201: 60 },
		KR: { 105: 60 },
		ID: { 103: 60 },
		GB: { 201: 60 },
		AU: { 201: 50, 101: 20 },
		VN: { 110: 60 },
		NL: { 201: 60 },
		TH: { 109: 50 },
		IN: { 102: 60 },
		MM: { 102: 60 },
		AE: { 501: 60 }, // Arabic
		CA: { 201: 60 },
	}

