import { formatName, nameConnector } from '@perkd/utils'
import { CleanseResult } from '../types'
import rfdc from 'rfdc'

const clone = rfdc(),
    MALAYSIA = 108,
    CHINESE = 101,
	FAMILY_GIVEN = 'familygiven',
	GIVEN_FAMILY = 'givenfamily'

export function setNamesProbabilityScore(
    data: { familyName?: string, givenName?: string }, 
    R: Partial<CleanseResult>
) {
    const { familyName = '', givenName = '' } = data,
        scores = {
            familyName: { family: 0, given: 0 },
            givenName: { family: 0, given: 0 },
        }

    const family = getFamilyNameProbability(R.words?.familyName, R)
    if (family) scores.familyName.family += family.familyName

    const given = getGivenNameProbability(R.words?.givenName, R)
    if (given) scores.familyName.given += given.givenName
    
    const family2 = getFamilyNameProbability(R.words?.givenName, R)
    if (family2) scores.givenName.family += family2.familyName

    const given2 = getGivenNameProbability(R.words?.familyName, R)
    if (given2) scores.givenName.given += given2.givenName

    const givenNameLength = R.words?.givenName.length ?? 0,
        givenNameFallbackLength = R.words?.fallback.givenName?.length ?? 0
    if (scores.familyName.family === 0 && R.ethnicity === CHINESE && givenNameLength <= 1) {
        switchName(R, familyName, givenName)
    } else if (scores.familyName.family + scores.familyName.given >= scores.givenName.family + scores.givenName.given) { // conservative about flipping
        R.familyName = formatName(familyName)
        R.givenName = formatName(givenName)
    } else if (R.ethnicity === CHINESE && (givenNameLength > 1 || givenNameFallbackLength > 1)) {
        R.familyName = formatName(familyName)
        R.givenName = formatName(givenName)
    } else {
        switchName(R, familyName, givenName)
    }

    R.scores = scores
}

export function setFullName(R: Partial<CleanseResult>) {
    splitName(R)
    const { name, characterSet, familyName, givenName } = R,
        spc = nameConnector(characterSet ?? '')

    switch (name?.order) {
        case FAMILY_GIVEN:
            R.fullName = (familyName + spc + givenName).trim()
            break
        case GIVEN_FAMILY:
            R.fullName = (givenName + spc + familyName).trim()
            break
        default:
            R.fullName = ''
            break
    }

    R.fullName = formatName(R.fullName)
}


/******************************** PRIVATE FUNCTIONS ****************************************/

function getFamilyNameProbability(familyNameWords: string[] = [], R: Partial<CleanseResult>) {
    let result: Record<string, number> | null = null

    for (const word of familyNameWords) {
        const name = R.names?.familyName.find(item => item.name === word)

        if (!name) continue
        if (name.familyName.probability < name.givenName.probability) continue
        if (name.familyName.count < R.threshold!.count) continue
        if (name.familyName.probability < R.threshold!.familyName) continue
        if (!result || name.familyName.probability > result.familyName) {
            result = result ?? { familyName: 0 }
            result.familyName = name.familyName.probability
        }
    }

    return result
}

function getGivenNameProbability(givenNameWords: string[] = [], R: Partial<CleanseResult>) {
    let result: Record<string, number> | null = null

    for (const word of givenNameWords) {
        const name = R.names?.givenName.find(item => item.name === word)

        if (!name) continue
        if (name.givenName.probability < name.familyName.probability) continue
        if (name.givenName.count < R.threshold!.count) continue
        if (name.givenName.probability < R.threshold!.givenName) continue
        if (!result || name.givenName.probability > result.givenName) {
            result = result ?? { givenName: 0 }
            result.givenName = name.givenName.probability
        }
    }

    return result
}

function switchName(R: Partial<CleanseResult>, familyName: string, givenName: string) {
    R.familyName = formatName(givenName)
    R.givenName = formatName(familyName)
    if(!R.words) return

    const words = clone(R.words)
    R.words.familyName = words.givenName
    R.words.givenName = words.familyName
    R.words.fallback.familyName = words.fallback.givenName ?? []
    R.words.fallback.givenName = words.fallback.familyName ?? []
}

function splitName(R: Partial<CleanseResult>) {
    if(R.ethnicity !== MALAYSIA) return

    // move the trailing 'Bin', 'Binte', 'Binti', 'Bte', 'Bt' in givenName to familyName
    const list = ['bin', 'binte', 'binti', 'bte', 'bt'], 
        lastGivenName = R.words?.givenName[R.words.givenName.length - 1] ?? '', 
        lastGivenNameLength = lastGivenName?.length

    if(!list.includes(lastGivenName)) return
    R.familyName = formatName(`${lastGivenName} ${R.familyName}`)
    R.givenName = R.givenName?.substring(0, R.givenName.length - lastGivenNameLength - 1)
}