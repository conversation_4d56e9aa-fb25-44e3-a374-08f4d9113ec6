
type BiasThreshold = {
    bias: {
        [key: string]: Record<string, number>
    }
    threshold: Record<string, number>
}
/**
 * Pick the value for Property in R(esult) with highest probablity score exceeding set threshold
*/
export function mostLikely(
    scores: Record<string, number>, 
    { bias, threshold }: BiasThreshold, 
    property: string
) {
	const most = moreLikely({ x: { [property]: scores } }, property, bias[property])
	return most ? (most.value >= threshold[property] ? most.key : null) : null
}

/**
 * Pick the value for Property in R(esult) with highest probablity score
 * @param  {Object} R        	collection of result objects, eg:
 *           { familyName: { ethinicity: { '101': 80, '201': 15 } }
 *             givenName: { ethnicity: { '400': 85 }, gender: { 'm': 100 } }
 *           }
 *
 * @param  {String} property  	name of property to shortlist
 * @param  {Object} bias	  	(optional) bias for property, eg.  { '102': 80, '201': 65 }
 * @return {Object}          	{ key: value} where key = value for Property, value = probability score
 */
export function moreLikely(
    R: { 
        [key: string]: {
            [key: string]: Record<string, number>
        } 
    }, 
    property: string, 
    bias: Record<string, number>
) {
	const scores: Record<string, number> = {}

	for (const res in R) {
		const prop = R[res][property]	// property of interest within this result object, eg.  ethincity: { }
		calculateScore(scores, prop)
	}
	calculateScore(scores, bias)		// apply bias

	const most = likely(scores)		// pick value with highest probability
	return most ? { key: most.key, value: most.value } : null
}

/**
 * Pick the value with highest probablity score from given collection
 * @param  {Object} probabilities  	collection of { key: probability } pairs, eg.
 *                                  { '101': 80, '201': 15 }
 * @return {Object}              	{ key: probability }
 */
export function likely(probabilities: Record<string, number>) {
    const highest = { key: '', value: -1 }
    for (const key in probabilities) {
        if(probabilities[key] > highest.value) {
            highest.key = key
            highest.value = probabilities[key]
        }
    }
    return highest
}

function calculateScore(scores: Record<string, number>, list?: Record<string, number>) {
    if (!list) return

    const FINE_TUNING = 1.2, TOTAL_SCORE = 2, MAX_PROPABILITY = 100
    for (const key in list) {
        scores[key] = scores[key] 
            ? Math.min((scores[key] + list[key]) * FINE_TUNING / TOTAL_SCORE, MAX_PROPABILITY)
            : list[key]
    }
}