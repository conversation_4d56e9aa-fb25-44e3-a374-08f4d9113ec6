import { NON_ENGLISH, CN_JP_KR_REGEX, SEPARATORS } from '../const'
import { splitIntoWords as SPLIT_INTO_WORDS } from '@perkd/utils'

type FallbackNames = {
	familyName?: string[]
	givenName?: string[]
}
type FamilyName = 'familyName'
type GivenName = 'givenName'
type Words = {
	familyName: string[]
	givenName: string[]
	fallback: FallbackNames
}

export function splitIntoWords(
	data: { 
		familyName?: string, 
		givenName?: string 
	}, 
	characterSet: string | null
) {
    let { familyName = '', givenName = '' } = data;
    const words: Words = {
        familyName: [],
        givenName: [],
		fallback: {}
    }

	if (!familyName && givenName) { // split givenName into familyName and givenName
        if (isNonEnglish(characterSet, givenName)) {
            const names = splitCharacters(givenName);
            data.familyName = names[0];
            data.givenName = names.slice(1).join('')
            words.familyName.push(names[0]);
            words.givenName.push(names.slice(1).join(''));
            familyName = names[0];
            givenName = names.slice(1).join('')
        }
        else {
            const names = SPLIT_INTO_WORDS(givenName);
            data.familyName = names[names.length-1];
            data.givenName = names.slice(0, names.length-1).join(' ')
            words.familyName.push(names[names.length-1]);
            words.givenName.push(names.slice(0, names.length-1).join(' '));
            familyName = names[names.length-1];
            givenName = names.slice(0, names.length-1).join(' ')
        }
	} else {
		if(familyName && isNonEnglish(characterSet, familyName)) {
			words.familyName.push(familyName)
			words.fallback.familyName = splitCharacters(familyName)
		}
	
		if(givenName && isNonEnglish(characterSet, givenName)) {
			words.givenName.push(givenName)
			words.fallback.givenName = splitCharacters(givenName)
		}
	}

	words.familyName = words.familyName.concat(SPLIT_INTO_WORDS(familyName))
	words.givenName = words.givenName.concat(SPLIT_INTO_WORDS(givenName))

	// TODO cleaner way, for chinese names
	// separateWordsKeyedInTogether('familyName', words)
	// separateWordsKeyedInTogether('givenName', words)

	return words
}

function splitCharacters(string: string) {
	return string.split('')
}

function isNonEnglish(language: string | null, name: string) {
	return (language && NON_ENGLISH.includes(language)) || CN_JP_KR_REGEX.test(name)
}

function separateWordsKeyedInTogether(name: FamilyName | GivenName, words: Words) {
	const names = words[name], { fallback } = words
	if (names.length !== 1 || names[0].length <= 3) return 

	SEPARATORS.some(l => {
		l.some(w => {
			const pos = names[0].indexOf(w, 2)
			if (pos > 0) {
				const name1 = names[0].substring(0, pos).replace(/[\d@()_~˙，-]/g, '').trim(),
					name2 = names[0].substring(pos).replace(/[\d@()_~˙，-]/g, '').trim()

				if (name1.length <= 1 || name2.length <= 1) return false

				fallback[name] = fallback[name] ?? []
				fallback[name]?.push(name1)
				fallback[name]?.push(name2)
				return true
			}
		})
		if (fallback[name]?.length ?? 0 > 0) return true
	})
}