import { CleanseResult } from "../types"
import { mostLikely } from "./likelihood"

const GENDER = 'gender'

export function setGender (data: { gender?: string | null }, R: Partial<CleanseResult>) {
    if (data.gender) { 
        R.gender = data.gender 
        return 
    }

    const score: Record<string, number> = {}
    for(const name of R.names?.givenName ?? []) {
        for(const gender in name.gender) {
            score[gender] = score[gender] ? score[gender] + name.gender[gender] : name.gender[gender]
        }
    }

    R.gender = mostLikely(score, { bias: R.bias ?? {}, threshold: R.threshold ?? {} }, GENDER)
}