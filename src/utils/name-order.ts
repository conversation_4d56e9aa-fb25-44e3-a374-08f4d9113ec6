import { FAMILY_GIVEN, GIVEN_FAMILY, NON_ENGLISH } from '../const'
import { CleanseResult, GivenName } from '../types'
import { likely } from './likelihood'

const FRENCH = '201',
    JAPANESE = '104',
    CHINESE = '101'


export function getOrderByLanguage(language?: string | null) {
    if(language && NON_ENGLISH.includes(language)) return FAMILY_GIVEN
    return null
}

export function getDefaultOrder(country: string) {
    const FIRST_NAME_1ST_COUNTRY = ['CN', 'IN', 'MG', 'LK', 'JP', 'KR', 'VN', 'KP', 'TW', 'HK', 'MO']
    return FIRST_NAME_1ST_COUNTRY.includes(country) ? FAMILY_GIVEN : GIVEN_FAMILY
}

export function aliasOrder(R: Partial<CleanseResult>) {
    // multiple English given name
    let ethnicity: string | null = getEthnicityFromGivenName(R.words?.givenName, R.names?.givenName, R.ethnicity)
        ?? getEthnicityFromGivenName(R.words?.fallback.givenName, R.names?.givenName, R.ethnicity)

    // GivenName not in Ref DB (R.names.givenName) i.e. ethinicity = null
    // Chinese & Vietnamese
    // single word, more likely to be an English Name.
    if (!ethnicity && R.ethnicity === CHINESE && R.words?.givenName.length === 1) {
        const givenName = R.names?.givenName.find(n => n.name === R.words?.givenName[0])
        if (!givenName) {
            ethnicity = CHINESE
        }
    }
    return (ethnicity === FRENCH || (ethnicity === JAPANESE && R.ethnicity === parseInt(CHINESE))) ? GIVEN_FAMILY : null
}

function getEthnicityFromGivenName(givenNameWords: string[] = [], dbNames: GivenName[] = [], ethnicity: string | number | null = '') {
    const SPECIAL_NAME = ['kit', 'kat', 'kim', 'wing']
    let result: string | null = null

    for(const word of givenNameWords) {
        const name = dbNames.find(n => n.name === word)
        if (!name) continue

        const { key } = likely(name.ethnicity) ?? {}
        if (!result || key && key !== FRENCH) result = key
        if (result === FRENCH 
            && SPECIAL_NAME.includes(word) 
            && givenNameWords.length > 1 
            && ethnicity === parseInt(CHINESE)
        ) result = CHINESE
    }

    return result
}