
import { detectLanguage } from '@perkd/utils'
import { SUPPORTED_LANGUAGE } from '../settings'
import { UNDEFINED } from '../const'

export function getLanguage({ fullName, familyName, givenName }: { fullName?: string, familyName?: string, givenName?: string }) {
    const language = detectLanguage(SUPPORTED_LANGUAGE, fullName || (familyName + ' ' + givenName));

    return language === UNDEFINED ? null : language;
}