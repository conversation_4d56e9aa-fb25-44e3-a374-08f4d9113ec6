
import { CN_JP_KR_REGEX } from '../const'

export function sanitize(
    data: {
        familyName?: string
        givenName?: string
    }
) {
    
    data.familyName = removeSpaceAndSpecialCharacters(data.familyName)
    data.givenName = removeSpaceAndSpecialCharacters(data.givenName)

    if (isEqual(data.familyName, data.givenName)) {
        data.familyName = ''
    }

    if (data.givenName && data.familyName) {
        data.givenName = sanitizeName(data.givenName, data.familyName)
        data.familyName = sanitizeName(data.familyName, data.givenName)
    }
}

/**
 * changes #1: Remove Leading or trailing spaces
 * changes #2: Remove special characters (“number”, “.”, “@“, “()”, “~”, “_”)
 * * only remove "." when the whole field is "."
 * @param name 
 * @returns 
 */
function removeSpaceAndSpecialCharacters(name?: string) {
    return (name && name.trim() !== '.') ? name.replace(/[\d@()_~˙，]/g, '').trim() : ''
}

function sanitizeName(name1: string, name2: string) {
    const name2Length = name2.length,
        cjk = CN_JP_KR_REGEX.test(name1),
        spaceLength = (cjk) ? 0 : 1
    if (isPrefix(name1, name2, cjk)) {
        name1 = name2.substring(name2Length + spaceLength)
    }
    if (isSuffix(name1, name2, cjk)) {
        name1 = name1.substring(0, name1.length - name2Length - spaceLength)
    }
    return name1
}

function isEqual(name1: string, name2: string) {
    return name1.toLowerCase() === name2.toLowerCase()
}

/**
 * name2 is prefix of name1
 * @param name1 
 * @param name2 
 * @param cjk boolean Chinese, Japanese, Korean
 * @returns boolean
 */
function isPrefix(name1: string, name2: string, cjk = false) {
    let spaceLength = 0
    if(!cjk) {
        name1 = name1.toLowerCase()
        name2 = name2.toLowerCase() + ' '
        spaceLength = 1
    }
    return name2 === name1.substring(0, name2.length + spaceLength)
}

/**
 * name2 is suffix of name2
 * @param name1 
 * @param name2 
 * @param cjk boolean Chinese, Japanese, Korean
 * @returns boolean
 */
function isSuffix(name1: string, name2: string, cjk = false) {
    let spaceLength = 0
    if(!cjk) {
        name1 = name1.toLowerCase()
        name2 = ' ' + name2.toLowerCase() + ' '
        spaceLength = 1
    }
    return name2 === name1.substring(name1.length - name2.length - spaceLength)
}

