import { CleanseResult, Names, Words } from '../types'
import { SPECIAL_NAMES, LANGUAGE_BIAS, COUNTRY_BIAS } from '../const'
import { mostLikely } from './likelihood'

const ETHNICITY = 'ethnicity'

/**
 * Methods:
 * 1. use given ethnicity
 * 2. use special names
 * 3. use db, most likely
 * 4. use languange and country 
 * @param data 
 * @param R 
 * @returns 
 */
export function setEthnicity(data: { ethnicity?: number | null }, R: Partial<CleanseResult>) {
    if (data.ethnicity) {
        R.ethnicity = data.ethnicity
        return
    }

    const ethnicityFromSpecialNames = getFromSpecialNames(R)
    if (ethnicityFromSpecialNames) {
        R.ethnicity = ethnicityFromSpecialNames
        return
    }

    const ethnicityFromDB = getFromDB(R)
    if (ethnicityFromDB) {
        R.ethnicity = parseInt(ethnicityFromDB)
        return
    }

    const ethnicityFromLanguageAndCountry = getFromLanguageAndCountry(R)
    if (ethnicityFromLanguageAndCountry) {
        R.ethnicity = parseInt(ethnicityFromLanguageAndCountry)
        return
    }

    R.ethnicity = null
    return
}


function getFromSpecialNames(R: Partial<CleanseResult>) {
    const { familyName = [], givenName = [] } = R.words ?? {},
        words = [ ...familyName, ...givenName ]

    for(const word of words) {
        if(SPECIAL_NAMES[word]) return SPECIAL_NAMES[word].ethnicity
    }
    return null
}

function getFromDB(R: Partial<CleanseResult>) {
    let ethnicity = null

    ethnicity = getFromDBWithHighestProbality(R.words!.familyName, R.names!, R.bias!, R.threshold!)
    if (!ethnicity) ethnicity = getFromDBWithHighestProbality(R.words!.givenName, R.names!, R.bias!, R.threshold!)

    return ethnicity
}

function getFromDBWithHighestProbality(
    names: string[], 
    nameList: Names, 
    bias: Record<string, Record<string, number>>, 
    threshold: Record<string, number>
) {
    const dbNames = [ ...nameList.familyName, ...nameList.givenName ],
        scores: Record<number, number> = {}

    for(const name of names) {
        const dbName = dbNames.find(item => item.name === name)
        if (!dbName) continue

        for(const eth in dbName.ethnicity) {
            if(!scores[eth] || scores[eth] < dbName.ethnicity[eth]) scores[eth] = dbName.ethnicity[eth]
        }
    }
    return mostLikely(scores, { bias, threshold }, ETHNICITY)
}

function getFromLanguageAndCountry (R: Partial<CleanseResult>) {
    const words = [ ...R.words?.familyName ?? [], ...R.words?.givenName ?? []],
        scores: Record<number, number> = {}

    let ethnicity = null
    if (words.length === 0) return ethnicity

    R.bias = { ethnicity: getBias(R.language ?? '', R.country ?? '') }
    ethnicity = mostLikely(scores, { bias: R.bias, threshold: R.threshold ?? {} }, ETHNICITY)
    return ethnicity
}

function getBias(language: string, country: string) {
    const languageBias = LANGUAGE_BIAS[language] ? { ...LANGUAGE_BIAS[language] } : {}
    const countryBias = COUNTRY_BIAS[country] ? { ...COUNTRY_BIAS[country] } : {}

    for (const i in languageBias) {
        if (countryBias[i]) { 
            countryBias[i] += languageBias[i] 
        } else countryBias[i] = languageBias[i]
    }
    return countryBias
}