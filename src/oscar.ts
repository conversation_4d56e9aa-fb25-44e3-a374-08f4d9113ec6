import { Db, MongoClient } from 'mongodb'
import { FAMILY_GIVEN, GIVEN_FAMILY, NON_ENGLISH } from './const'
import {  DEFAULT_SETTING, SUPPORTE_LANGUAGED_MAPPING } from './settings'
import { CleanseResult, Names } from './types'
import { aliasOrder, getDefaultOrder, getOrderByLanguage, sanitize, getLanguage, splitIntoWords, setEthnicity, setGender, setFullName, setNamesProbabilityScore } from './utils'

const { 
		NODE_ENV,
		DB_HOST = NODE_ENV ? 'mongodb' : '127.0.0.1',
		DB_USERNAME = '',
		DB_PASSWORD = '',
		DB_AUTH = 'admin',
		DB_SET = '',
	} = process.env,
	URI = 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${encodeURIComponent(DB_PASSWORD)}@` : '') + `${DB_HOST}/oscar?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}` : ''),
	DB = 'oscar'

enum Collection {
	GIVEN_NAME = 'GivenName',
	FAMILY_NAME = 'FamilyName',
	NAME_ORDER = 'NameOrder'
}

export class Oscar {
	private dbClient: MongoClient
	private db: Db | null

	constructor() {
		this.dbClient = new MongoClient(URI)
		this.db = null
	}

	async init() {
		await this.connect()
	}

	async stop() {
		await this.dbClient.close()
	}

	async connect() {
		try {
			await this.dbClient.connect()
			this.db = this.dbClient.db(DB)
			// console.log(`Successfully connected to db ${this.db.databaseName}`)
		} catch (err) {
			console.error(`we encountered ${err}`)
		}
	}

	async clean(
		data: { 
			firstName?: string
			givenName?: string
			name?: {
				order?: string | null
				displayAs?: string | null
			}
			gender?: string | null
			locale?: {
				country?: string
				languages?: string[]
			}
			ethnicity?: number | null
		}, 
		options = {}
	) {
		const { locale, name, gender = null } = data,
			{ country = '', languages = [] } = locale ?? {},
			language = languages?.[0] ?? '',
			displayAs = name?.displayAs ?? null,
			opt = {
				...DEFAULT_SETTING,
				...options
			}

		sanitize(data)

		const
			R: Partial<CleanseResult> = {
				language: (language && SUPPORTE_LANGUAGED_MAPPING[language.substring(0, 2)]) 
					? SUPPORTE_LANGUAGED_MAPPING[language.substring(0, 2)] 
					: getLanguage(data),
				characterSet: getLanguage(data),
				country,
				customer: {
					displayAs,
					gender,
				},
				bias: opt.bias,
				threshold: opt.threshold,
				words: splitIntoWords(data, getLanguage(data))
			}

		R.names = await this.getNames(R.words?.familyName.concat(
				R.words?.givenName, 
				R.words?.fallback.familyName ?? [], 
				R.words?.fallback.givenName ?? []
			) ?? [])
		setEthnicity(data, R)
		setGender(data, R)
		setNamesProbabilityScore(data, R)
		await this.setNameOrder(name?.order ?? '', R)
		setFullName(R)

		return R
	}

	async setNameOrder(order: string, R: Partial<CleanseResult>) {
		if (order) { 
			R.name = { order }
			return 
		}

		const { familyName, givenName, characterSet, ethnicity, customer, language, country = '' } = R
		if (!familyName && !givenName) {
			R.name = { order: null }
			return
		}
		if (!familyName) {
			R.name = { order: GIVEN_FAMILY }
			return
		}
		if (!givenName || NON_ENGLISH.includes(characterSet ?? '')) {
			R.name = { order: FAMILY_GIVEN }
			return
		}

		if (ethnicity) {
			const nameOrder = await this.db?.collection(Collection.NAME_ORDER).findOne({ _id: ethnicity as any }),
				order = nameOrder?.order

			R.name = {
				order: getOrderByLanguage(characterSet)
					?? aliasOrder(R)
					?? getOrderByLanguage(language)
					?? order
					?? R.bias?.nameOrder
					?? getDefaultOrder(country)
			}
			return
		}
		R.name = { order: aliasOrder(R) ?? customer?.displayAs ?? getDefaultOrder(country) }
		console.log('NameOrderNotMatch', { R })
	}

	async getNames(names: string[]) {
		const filter = { name: { $in: names } },
			options = { projection: { _id: false } }

		const givenNameQuery = this.db?.collection(Collection.GIVEN_NAME).find(filter, options),
			familyNameQuery = this.db?.collection(Collection.FAMILY_NAME).find(filter, options)

		const [ givenName, familyName ] = await Promise.all([
			givenNameQuery?.toArray(),
			familyNameQuery?.toArray()
		])

		return { givenName, familyName } as unknown as Names
	}
}