{"name": "@perkd/oscar", "version": "1.2.1", "description": "", "author": "", "engines": {"node": ">=20"}, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "npx tsc", "pretest": "npm run build", "test": "tsc; node -r ts-node/register --test tests/*.test.ts", "test-one": "tsc; node -r ts-node/register --test", "reinstall": "rm -rf node_modules/ yarn.lock; yarn; rm -rf dist/ tsconfig.tsbuildinfo; tsc"}, "repository": {"type": "git", "url": "git+ssh://**************/oscar.git"}, "bugs": {"url": "https://github.com/perkd/oscar/issues"}, "homepage": "https://github.com/perkd/oscar#readme", "dependencies": {"@perkd/utils": "github:perkd/utils#semver:^1.9.9", "mongodb": "^6.15.0", "rfdc": "^1.4.1"}, "devDependencies": {"@perkd/eslint-config": "github:Perkd-X/eslint-config#semver:^3.1.3", "@types/node": "^22.14.1", "@typescript-eslint/eslint-plugin": "^8.30.1", "ts-node": "^10.9.2", "tslib": "^2.8.1", "typescript": "^5.8.3"}, "packageManager": "yarn@4.9.1"}