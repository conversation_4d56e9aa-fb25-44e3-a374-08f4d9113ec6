{"$schema": "http://json.schemastore.org/tsconfig", "compilerOptions": {"outDir": "dist", "rootDir": "src", "resolveJsonModule": true, "skipLibCheck": true, "strict": true, "strictPropertyInitialization": false, "module": "commonjs", "moduleResolution": "node", "esModuleInterop": true, "lib": ["ES2022"], "incremental": true, "composite": true, "target": "ES2022", "sourceMap": true, "declaration": true, "importHelpers": true}, "include": ["src/**/*", "src/*.json", "src/**/*.json"], "exclude": ["node_modules/**", "tests", "**/*.d.ts"]}