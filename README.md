# @perkd/oscar

A sophisticated name parsing and processing library that handles names across different cultures, languages, and formats. The library specializes in determining name order, ethnicity, gender, and proper formatting of names.

## Features

### Name Parsing and Sanitization
- Intelligent word splitting for different writing systems
- Handles mixed scripts (e.g., Latin + CJK characters)
- Removes unwanted characters and normalizes whitespace
- Preserves special characters (e.g., hyphens, apostrophes) when appropriate

### Cultural-Aware Name Order Detection
- Automatically detects proper name order (given-family vs family-given)
- Supports cultural preferences for name ordering
- Handles special cases like:
  - Chinese names (family-given)
  - Western names (given-family)
  - Spanish/Portuguese compound names
  - Vietnamese compound family names

### Ethnicity Detection
- Uses machine learning models to predict likely ethnicities
- Supports multiple possible ethnicities with confidence scores
- Takes into account:
  - Character sets used
  - Name patterns
  - Regional statistics
  - Historical data

### Gender Inference
- Predicts likely gender based on given names
- Culturally aware gender detection
- Handles unisex names with probability scores
- Option to override with known gender

### Multi-Language Support
- Supports 25+ languages including:
  - East Asian: Chinese, Japanese, Korean
  - Southeast Asian: Thai, Vietnamese, Malay
  - South Asian: Hindi, Bengali, Tamil
  - European: English, Spanish, French, German
  - Middle Eastern: Arabic, Hebrew
- Handles mixed-language names
- Language-specific formatting rules

### MongoDB Integration
- Efficient name lookup using MongoDB
- Stores and retrieves:
  - Given name statistics
  - Family name statistics
  - Cultural ordering preferences
  - Regional variations

### Configurable Settings
- Adjustable bias settings for:
  - Name order preferences
  - Gender detection
  - Ethnicity matching
- Customizable threshold values
- Flexible formatting options

### Rich Response Data
- Detailed analysis results
- Confidence scores
- Multiple formatting options
- Metadata about the processing decisions

## Requirements

- Node.js >= 18
- MongoDB instance
- Yarn (preferred package manager)

## Environment Variables

```bash
NODE_ENV=development
DB_HOST=127.0.0.1      # MongoDB host (defaults to 'mongodb' in production)
DB_USERNAME=           # MongoDB username
DB_PASSWORD=           # MongoDB password
DB_AUTH=admin         # MongoDB auth database
DB_SET=               # MongoDB replica set name (optional)
```

## Usage

```typescript
import { Oscar } from '@perkd/oscar';

// Initialize Oscar
const oscar = new Oscar();
await oscar.init();

// Clean and process a name
const result = await oscar.clean({
  familyName: 'Zhang',
  givenName: 'Wei',
  locale: {
    country: 'CN',
    languages: ['zh']
  }
});

// Close connection when done
await oscar.stop();
```

## Response Format

The cleanse operation returns a `CleanseResult` object containing:

```typescript
{
  familyName: string;          // Processed family name
  givenName: string;          // Processed given name
  fullName: string;           // Formatted full name
  name: {
    order: string | null;     // 'familygiven' or 'givenfamily'
  };
  characterSet: string | null; // Detected character set
  ethnicity: number | null;    // Detected ethnicity code
  language: string | null;     // Detected language
  country: string;            // Country code
  gender: string | null;       // Detected gender
  // ... additional metadata
}
```

## Supported Languages

The library supports multiple languages including:
- English (eng)
- Chinese (cmn)
- Japanese (jpn)
- Korean (kor)
- Malay (mal)
- Vietnamese (vie)
- Thai (tha)
- And many more
