{"version": 3, "file": "oscar.js", "sourceRoot": "", "sources": ["../src/oscar.ts"], "names": [], "mappings": ";;;AAAA,qCAAyC;AACzC,mCAAiE;AACjE,yCAAyE;AAEzE,mCAAgL;AAEhL,MAAM,EACJ,QAAQ,EACR,OAAO,GAAG,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,WAAW,EAC5C,WAAW,GAAG,EAAE,EAChB,WAAW,GAAG,EAAE,EAChB,OAAO,GAAG,OAAO,EACjB,MAAM,GAAG,EAAE,GACX,GAAG,OAAO,CAAC,GAAG,EACf,GAAG,GAAG,YAAY,GAAG,CAAC,WAAW,CAAC,CAAC,CAAC,GAAG,WAAW,IAAI,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,GAAG,OAAO,qBAAqB,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,CAAC,eAAe,MAAM,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC,EAC/J,EAAE,GAAG,OAAO,CAAA;AAEb,IAAK,UAIJ;AAJD,WAAK,UAAU;IACd,sCAAwB,CAAA;IACxB,wCAA0B,CAAA;IAC1B,sCAAwB,CAAA;AACzB,CAAC,EAJI,UAAU,KAAV,UAAU,QAId;AAED,MAAa,KAAK;IACT,QAAQ,CAAa;IACrB,EAAE,CAAW;IAErB;QACC,IAAI,CAAC,QAAQ,GAAG,IAAI,qBAAW,CAAC,GAAG,CAAC,CAAA;QACpC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAA;IACf,CAAC;IAED,KAAK,CAAC,IAAI;QACT,MAAM,IAAI,CAAC,OAAO,EAAE,CAAA;IACrB,CAAC;IAED,KAAK,CAAC,IAAI;QACT,MAAM,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAA;IAC5B,CAAC;IAED,KAAK,CAAC,OAAO;QACZ,IAAI,CAAC;YACJ,MAAM,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE,CAAA;YAC7B,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAA;YAC9B,sEAAsE;QACvE,CAAC;QAAC,OAAO,GAAG,EAAE,CAAC;YACd,OAAO,CAAC,KAAK,CAAC,kBAAkB,GAAG,EAAE,CAAC,CAAA;QACvC,CAAC;IACF,CAAC;IAED,KAAK,CAAC,KAAK,CACV,IAaC,EACD,OAAO,GAAG,EAAE;QAEZ,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,MAAM,GAAG,IAAI,EAAE,GAAG,IAAI,EAC3C,EAAE,OAAO,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,MAAM,IAAI,EAAE,EAC/C,QAAQ,GAAG,SAAS,EAAE,CAAC,CAAC,CAAC,IAAI,EAAE,EAC/B,SAAS,GAAG,IAAI,EAAE,SAAS,IAAI,IAAI,EACnC,GAAG,GAAG;YACL,GAAG,0BAAe;YAClB,GAAG,OAAO;SACV,CAAA;QAEF,IAAA,gBAAQ,EAAC,IAAI,CAAC,CAAA;QAEd,MACC,CAAC,GAA2B;YAC3B,QAAQ,EAAE,CAAC,QAAQ,IAAI,qCAA0B,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBAC3E,CAAC,CAAC,qCAA0B,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;gBACtD,CAAC,CAAC,IAAA,mBAAW,EAAC,IAAI,CAAC;YACpB,YAAY,EAAE,IAAA,mBAAW,EAAC,IAAI,CAAC;YAC/B,OAAO;YACP,QAAQ,EAAE;gBACT,SAAS;gBACT,MAAM;aACN;YACD,IAAI,EAAE,GAAG,CAAC,IAAI;YACd,SAAS,EAAE,GAAG,CAAC,SAAS;YACxB,KAAK,EAAE,IAAA,sBAAc,EAAC,IAAI,EAAE,IAAA,mBAAW,EAAC,IAAI,CAAC,CAAC;SAC9C,CAAA;QAEF,CAAC,CAAC,KAAK,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,MAAM,CACtD,CAAC,CAAC,KAAK,EAAE,SAAS,EAClB,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,UAAU,IAAI,EAAE,EAClC,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,SAAS,IAAI,EAAE,CACjC,IAAI,EAAE,CAAC,CAAA;QACT,IAAA,oBAAY,EAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QACrB,IAAA,iBAAS,EAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QAClB,IAAA,gCAAwB,EAAC,IAAI,EAAE,CAAC,CAAC,CAAA;QACjC,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE,KAAK,IAAI,EAAE,EAAE,CAAC,CAAC,CAAA;QAC7C,IAAA,mBAAW,EAAC,CAAC,CAAC,CAAA;QAEd,OAAO,CAAC,CAAA;IACT,CAAC;IAED,KAAK,CAAC,YAAY,CAAC,KAAa,EAAE,CAAyB;QAC1D,IAAI,KAAK,EAAE,CAAC;YACX,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,EAAE,CAAA;YAClB,OAAM;QACP,CAAC;QAED,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,CAAC,CAAA;QAC9F,IAAI,CAAC,UAAU,IAAI,CAAC,SAAS,EAAE,CAAC;YAC/B,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,EAAE,IAAI,EAAE,CAAA;YACxB,OAAM;QACP,CAAC;QACD,IAAI,CAAC,UAAU,EAAE,CAAC;YACjB,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,EAAE,oBAAY,EAAE,CAAA;YAChC,OAAM;QACP,CAAC;QACD,IAAI,CAAC,SAAS,IAAI,mBAAW,CAAC,QAAQ,CAAC,YAAY,IAAI,EAAE,CAAC,EAAE,CAAC;YAC5D,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,EAAE,oBAAY,EAAE,CAAA;YAChC,OAAM;QACP,CAAC;QAED,IAAI,SAAS,EAAE,CAAC;YACf,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,OAAO,CAAC,EAAE,GAAG,EAAE,SAAgB,EAAE,CAAC,EACpG,KAAK,GAAG,SAAS,EAAE,KAAK,CAAA;YAEzB,CAAC,CAAC,IAAI,GAAG;gBACR,KAAK,EAAE,IAAA,0BAAkB,EAAC,YAAY,CAAC;uBACnC,IAAA,kBAAU,EAAC,CAAC,CAAC;uBACb,IAAA,0BAAkB,EAAC,QAAQ,CAAC;uBAC5B,KAAK;uBACL,CAAC,CAAC,IAAI,EAAE,SAAS;uBACjB,IAAA,uBAAe,EAAC,OAAO,CAAC;aAC5B,CAAA;YACD,OAAM;QACP,CAAC;QACD,CAAC,CAAC,IAAI,GAAG,EAAE,KAAK,EAAE,IAAA,kBAAU,EAAC,CAAC,CAAC,IAAI,QAAQ,EAAE,SAAS,IAAI,IAAA,uBAAe,EAAC,OAAO,CAAC,EAAE,CAAA;QACpF,OAAO,CAAC,GAAG,CAAC,mBAAmB,EAAE,EAAE,CAAC,EAAE,CAAC,CAAA;IACxC,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,KAAe;QAC7B,MAAM,MAAM,GAAG,EAAE,IAAI,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,EACtC,OAAO,GAAG,EAAE,UAAU,EAAE,EAAE,GAAG,EAAE,KAAK,EAAE,EAAE,CAAA;QAEzC,MAAM,cAAc,GAAG,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,UAAU,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,EACtF,eAAe,GAAG,IAAI,CAAC,EAAE,EAAE,UAAU,CAAC,UAAU,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAA;QAEpF,MAAM,CAAE,SAAS,EAAE,UAAU,CAAE,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACnD,cAAc,EAAE,OAAO,EAAE;YACzB,eAAe,EAAE,OAAO,EAAE;SAC1B,CAAC,CAAA;QAEF,OAAO,EAAE,SAAS,EAAE,UAAU,EAAsB,CAAA;IACrD,CAAC;CACD;AAzID,sBAyIC"}