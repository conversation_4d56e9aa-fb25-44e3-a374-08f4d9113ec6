"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.COUNTRY_BIAS = exports.LANGUAGE_BIAS = exports.SPECIAL_NAMES = exports.SEPARATORS = exports.CN_JP_KR_REGEX = exports.NON_ENGLISH = exports.KOREAN = exports.JAPANESE = exports.CHINESE = exports.UNDEFINED = exports.GIVEN_FAMILY = exports.FAMILY_GIVEN = void 0;
exports.FAMILY_GIVEN = 'familygiven', exports.GIVEN_FAMILY = 'givenfamily', exports.UNDEFINED = 'und', exports.CHINESE = 'cmn', exports.JAPANESE = 'jpn', exports.KOREAN = 'kor', exports.NON_ENGLISH = [exports.CHINESE, exports.JAPANESE, exports.KOREAN], exports.CN_JP_KR_REGEX = /^[\u4e00-\u9fa5\uac00-\ud7ff\u0800-\u4e00]+$/, exports.SEPARATORS = [
    ['-', 'zh', 'ch', 'sh', 'hs', 'ts', 'sw', 'sz', 'tz'],
    ['w', 'x', 'y', 'z', 'b', 'p', 'f', 'd', 'l', 'j', 'q', 'c', 's'],
    ['m', 'k', 't', 'h'],
    ['r', 'n', 'g'],
], exports.SPECIAL_NAMES = {
    // bin: { ethnicity: 108, gender: 1, bias: { ethnicity: 50 } },
    ali: { ethnicity: 108, gender: 1 },
    muhammad: { ethnicity: 108, gender: 1 },
    mohammad: { ethnicity: 108, gender: 1 },
    mohammed: { ethnicity: 108, gender: 1 },
    muhd: { ethnicity: 108, gender: 1 },
    mohd: { ethnicity: 108, gender: 1 },
    mamat: { ethnicity: 108, gender: 1 },
    ahmad: { ethnicity: 108, gender: 1 },
    abdul: { ethnicity: 108, gender: 1 },
    binte: { ethnicity: 108, gender: 2 },
    binti: { ethnicity: 108, gender: 2 },
    bte: { ethnicity: 108, gender: 2 },
    bt: { ethnicity: 108, gender: 2 },
    nur: { ethnicity: 108, gender: 2 },
    nurul: { ethnicity: 108, gender: 2 },
    noor: { ethnicity: 108, gender: 2 },
    nor: { ethnicity: 108, gender: 2 },
    siti: { ethnicity: 108, gender: 2 },
}, exports.LANGUAGE_BIAS = {
    cmn: { 101: 80 },
    eng: {},
    jpn: { 104: 80 },
    kor: { 105: 80 },
    vie: { 110: 80 },
    mal: { 108: 80 },
    ind: { 103: 80 },
    fra: { 201: 80 }, // French
    tha: { 109: 80 },
    deu: { 201: 80 }, // German
    spa: { 107: 50, 201: 50 }, // Spanish
    // in: { 102: 80 }, // language?
    por: { 107: 50, 201: 50 }, // Portuguese
    rus: { 201: 80 }, // Russian
    ita: { 201: 80 }, // Italian
    arb: { 501: 80 }, // Arabic
    nld: { 201: 80 }, // Dutch
    swe: { 201: 80 }, // Swedish
}, exports.COUNTRY_BIAS = {
    SG: { 101: 35, 108: 10, 102: 5 },
    TW: { 101: 50 },
    HK: { 101: 50 },
    CN: { 101: 60 },
    MY: { 108: 60, 101: 20 },
    JP: { 104: 60 },
    MO: { 101: 50 },
    PH: { 107: 60 },
    US: { 201: 60 },
    KR: { 105: 60 },
    ID: { 103: 60 },
    GB: { 201: 60 },
    AU: { 201: 50, 101: 20 },
    VN: { 110: 60 },
    NL: { 201: 60 },
    TH: { 109: 50 },
    IN: { 102: 60 },
    MM: { 102: 60 },
    AE: { 501: 60 }, // Arabic
    CA: { 201: 60 },
};
//# sourceMappingURL=const.js.map