type FallbackNames = {
    familyName?: string[];
    givenName?: string[];
};
type FamilyName = {
    name: string;
    familyName: {
        count: number;
        probability: number;
    };
    givenName: {
        count: number;
        probability: number;
    };
    ethnicity: Record<number, number>;
};
export type GivenName = FamilyName & {
    gender?: Record<string, number>;
};
export type Words = {
    familyName: string[];
    givenName: string[];
    fallback: FallbackNames;
};
export type Names = {
    givenName: GivenName[];
    familyName: FamilyName[];
};
export type CleanseResult = {
    familyName: string;
    givenName: string;
    fullName: string;
    name: {
        order: string | null;
    };
    characterSet: string | null;
    ethnicity: number | string | null;
    words: Words;
    names: Names;
    language: string | null;
    country: string;
    bias: Record<string, Record<string, number>>;
    threshold: Record<string, number>;
    gender: string | null;
    scores: {
        familyName: {
            family: number;
            given: number;
        };
        givenName: {
            family: number;
            given: number;
        };
    };
    customer: {
        displayAs: string | null;
        gender: string | null;
    };
};
export {};
