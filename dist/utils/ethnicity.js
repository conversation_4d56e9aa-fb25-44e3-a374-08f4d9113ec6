"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setEthnicity = setEthnicity;
const const_1 = require("../const");
const likelihood_1 = require("./likelihood");
const ETHNICITY = 'ethnicity';
/**
 * Methods:
 * 1. use given ethnicity
 * 2. use special names
 * 3. use db, most likely
 * 4. use languange and country
 * @param data
 * @param R
 * @returns
 */
function setEthnicity(data, R) {
    if (data.ethnicity) {
        R.ethnicity = data.ethnicity;
        return;
    }
    const ethnicityFromSpecialNames = getFromSpecialNames(R);
    if (ethnicityFromSpecialNames) {
        R.ethnicity = ethnicityFromSpecialNames;
        return;
    }
    const ethnicityFromDB = getFromDB(R);
    if (ethnicityFromDB) {
        R.ethnicity = parseInt(ethnicityFromDB);
        return;
    }
    const ethnicityFromLanguageAndCountry = getFromLanguageAndCountry(R);
    if (ethnicityFromLanguageAndCountry) {
        R.ethnicity = parseInt(ethnicityFromLanguageAndCountry);
        return;
    }
    R.ethnicity = null;
    return;
}
function getFromSpecialNames(R) {
    const { familyName = [], givenName = [] } = R.words ?? {}, words = [...familyName, ...givenName];
    for (const word of words) {
        if (const_1.SPECIAL_NAMES[word])
            return const_1.SPECIAL_NAMES[word].ethnicity;
    }
    return null;
}
function getFromDB(R) {
    let ethnicity = null;
    ethnicity = getFromDBWithHighestProbality(R.words.familyName, R.names, R.bias, R.threshold);
    if (!ethnicity)
        ethnicity = getFromDBWithHighestProbality(R.words.givenName, R.names, R.bias, R.threshold);
    return ethnicity;
}
function getFromDBWithHighestProbality(names, nameList, bias, threshold) {
    const dbNames = [...nameList.familyName, ...nameList.givenName], scores = {};
    for (const name of names) {
        const dbName = dbNames.find(item => item.name === name);
        if (!dbName)
            continue;
        for (const eth in dbName.ethnicity) {
            if (!scores[eth] || scores[eth] < dbName.ethnicity[eth])
                scores[eth] = dbName.ethnicity[eth];
        }
    }
    return (0, likelihood_1.mostLikely)(scores, { bias, threshold }, ETHNICITY);
}
function getFromLanguageAndCountry(R) {
    const words = [...R.words?.familyName ?? [], ...R.words?.givenName ?? []], scores = {};
    let ethnicity = null;
    if (words.length === 0)
        return ethnicity;
    R.bias = { ethnicity: getBias(R.language ?? '', R.country ?? '') };
    ethnicity = (0, likelihood_1.mostLikely)(scores, { bias: R.bias, threshold: R.threshold ?? {} }, ETHNICITY);
    return ethnicity;
}
function getBias(language, country) {
    const languageBias = const_1.LANGUAGE_BIAS[language] ? { ...const_1.LANGUAGE_BIAS[language] } : {};
    const countryBias = const_1.COUNTRY_BIAS[country] ? { ...const_1.COUNTRY_BIAS[country] } : {};
    for (const i in languageBias) {
        if (countryBias[i]) {
            countryBias[i] += languageBias[i];
        }
        else
            countryBias[i] = languageBias[i];
    }
    return countryBias;
}
//# sourceMappingURL=ethnicity.js.map