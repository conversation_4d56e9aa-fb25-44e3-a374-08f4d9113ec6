"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setGender = setGender;
const likelihood_1 = require("./likelihood");
const GENDER = 'gender';
function setGender(data, R) {
    if (data.gender) {
        R.gender = data.gender;
        return;
    }
    const score = {};
    for (const name of R.names?.givenName ?? []) {
        for (const gender in name.gender) {
            score[gender] = score[gender] ? score[gender] + name.gender[gender] : name.gender[gender];
        }
    }
    R.gender = (0, likelihood_1.mostLikely)(score, { bias: R.bias ?? {}, threshold: R.threshold ?? {} }, GENDER);
}
//# sourceMappingURL=gender.js.map