{"version": 3, "file": "sanitize.js", "sourceRoot": "", "sources": ["../../src/utils/sanitize.ts"], "names": [], "mappings": ";;AAGA,4BAkBC;AApBD,oCAAyC;AAEzC,SAAgB,QAAQ,CACpB,IAGC;IAGD,IAAI,CAAC,UAAU,GAAG,+BAA+B,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;IAClE,IAAI,CAAC,SAAS,GAAG,+BAA+B,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;IAEhE,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC;QAC3C,IAAI,CAAC,UAAU,GAAG,EAAE,CAAA;IACxB,CAAC;IAED,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,UAAU,EAAE,CAAC;QACpC,IAAI,CAAC,SAAS,GAAG,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,UAAU,CAAC,CAAA;QAC9D,IAAI,CAAC,UAAU,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,CAAC,CAAA;IACnE,CAAC;AACL,CAAC;AAED;;;;;;GAMG;AACH,SAAS,+BAA+B,CAAC,IAAa;IAClD,OAAO,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,cAAc,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAC,CAAC,CAAC,EAAE,CAAA;AACvF,CAAC;AAED,SAAS,YAAY,CAAC,KAAa,EAAE,KAAa;IAC9C,MAAM,WAAW,GAAG,KAAK,CAAC,MAAM,EAC5B,GAAG,GAAG,sBAAc,CAAC,IAAI,CAAC,KAAK,CAAC,EAChC,WAAW,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAA;IAC/B,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC;QAC9B,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,WAAW,GAAG,WAAW,CAAC,CAAA;IACtD,CAAC;IACD,IAAI,QAAQ,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,CAAC,EAAE,CAAC;QAC9B,KAAK,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,WAAW,GAAG,WAAW,CAAC,CAAA;IACxE,CAAC;IACD,OAAO,KAAK,CAAA;AAChB,CAAC;AAED,SAAS,OAAO,CAAC,KAAa,EAAE,KAAa;IACzC,OAAO,KAAK,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,WAAW,EAAE,CAAA;AACtD,CAAC;AAED;;;;;;GAMG;AACH,SAAS,QAAQ,CAAC,KAAa,EAAE,KAAa,EAAE,GAAG,GAAG,KAAK;IACvD,IAAI,WAAW,GAAG,CAAC,CAAA;IACnB,IAAG,CAAC,GAAG,EAAE,CAAC;QACN,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAA;QAC3B,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,GAAG,CAAA;QACjC,WAAW,GAAG,CAAC,CAAA;IACnB,CAAC;IACD,OAAO,KAAK,KAAK,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,CAAA;AACnE,CAAC;AAED;;;;;;GAMG;AACH,SAAS,QAAQ,CAAC,KAAa,EAAE,KAAa,EAAE,GAAG,GAAG,KAAK;IACvD,IAAI,WAAW,GAAG,CAAC,CAAA;IACnB,IAAG,CAAC,GAAG,EAAE,CAAC;QACN,KAAK,GAAG,KAAK,CAAC,WAAW,EAAE,CAAA;QAC3B,KAAK,GAAG,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,GAAG,GAAG,CAAA;QACvC,WAAW,GAAG,CAAC,CAAA;IACnB,CAAC;IACD,OAAO,KAAK,KAAK,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,MAAM,GAAG,WAAW,CAAC,CAAA;AAC/E,CAAC"}