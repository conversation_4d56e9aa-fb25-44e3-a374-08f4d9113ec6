{"version": 3, "file": "names.js", "sourceRoot": "", "sources": ["../../src/utils/names.ts"], "names": [], "mappings": ";;AAUA,4DAqCC;AAED,kCAkBC;;AAnED,wCAAwD;AAExD,wDAAuB;AAEvB,MAAM,KAAK,GAAG,IAAA,cAAI,GAAE,EAChB,QAAQ,GAAG,GAAG,EACd,OAAO,GAAG,GAAG,EAChB,YAAY,GAAG,aAAa,EAC5B,YAAY,GAAG,aAAa,CAAA;AAE7B,SAAgB,wBAAwB,CACpC,IAAiD,EACjD,CAAyB;IAEzB,MAAM,EAAE,UAAU,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,IAAI,EAC5C,MAAM,GAAG;QACL,UAAU,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;QACnC,SAAS,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE;KACrC,CAAA;IAEL,MAAM,MAAM,GAAG,wBAAwB,CAAC,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,CAAA;IAC/D,IAAI,MAAM;QAAE,MAAM,CAAC,UAAU,CAAC,MAAM,IAAI,MAAM,CAAC,UAAU,CAAA;IAEzD,MAAM,KAAK,GAAG,uBAAuB,CAAC,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAA;IAC5D,IAAI,KAAK;QAAE,MAAM,CAAC,UAAU,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,CAAA;IAErD,MAAM,OAAO,GAAG,wBAAwB,CAAC,CAAC,CAAC,KAAK,EAAE,SAAS,EAAE,CAAC,CAAC,CAAA;IAC/D,IAAI,OAAO;QAAE,MAAM,CAAC,SAAS,CAAC,MAAM,IAAI,OAAO,CAAC,UAAU,CAAA;IAE1D,MAAM,MAAM,GAAG,uBAAuB,CAAC,CAAC,CAAC,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC,CAAA;IAC9D,IAAI,MAAM;QAAE,MAAM,CAAC,SAAS,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,CAAA;IAEtD,MAAM,eAAe,GAAG,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,MAAM,IAAI,CAAC,EAClD,uBAAuB,GAAG,CAAC,CAAC,KAAK,EAAE,QAAQ,CAAC,SAAS,EAAE,MAAM,IAAI,CAAC,CAAA;IACtE,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,OAAO,IAAI,eAAe,IAAI,CAAC,EAAE,CAAC;QACpF,UAAU,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,CAAA;IACxC,CAAC;SAAM,IAAI,MAAM,CAAC,UAAU,CAAC,MAAM,GAAG,MAAM,CAAC,UAAU,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,CAAC,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC,CAAC,8BAA8B;QAC/I,CAAC,CAAC,UAAU,GAAG,IAAA,kBAAU,EAAC,UAAU,CAAC,CAAA;QACrC,CAAC,CAAC,SAAS,GAAG,IAAA,kBAAU,EAAC,SAAS,CAAC,CAAA;IACvC,CAAC;SAAM,IAAI,CAAC,CAAC,SAAS,KAAK,OAAO,IAAI,CAAC,eAAe,GAAG,CAAC,IAAI,uBAAuB,GAAG,CAAC,CAAC,EAAE,CAAC;QACzF,CAAC,CAAC,UAAU,GAAG,IAAA,kBAAU,EAAC,UAAU,CAAC,CAAA;QACrC,CAAC,CAAC,SAAS,GAAG,IAAA,kBAAU,EAAC,SAAS,CAAC,CAAA;IACvC,CAAC;SAAM,CAAC;QACJ,UAAU,CAAC,CAAC,EAAE,UAAU,EAAE,SAAS,CAAC,CAAA;IACxC,CAAC;IAED,CAAC,CAAC,MAAM,GAAG,MAAM,CAAA;AACrB,CAAC;AAED,SAAgB,WAAW,CAAC,CAAyB;IACjD,SAAS,CAAC,CAAC,CAAC,CAAA;IACZ,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAC,EACnD,GAAG,GAAG,IAAA,qBAAa,EAAC,YAAY,IAAI,EAAE,CAAC,CAAA;IAE3C,QAAQ,IAAI,EAAE,KAAK,EAAE,CAAC;QAClB,KAAK,YAAY;YACb,CAAC,CAAC,QAAQ,GAAG,CAAC,UAAU,GAAG,GAAG,GAAG,SAAS,CAAC,CAAC,IAAI,EAAE,CAAA;YAClD,MAAK;QACT,KAAK,YAAY;YACb,CAAC,CAAC,QAAQ,GAAG,CAAC,SAAS,GAAG,GAAG,GAAG,UAAU,CAAC,CAAC,IAAI,EAAE,CAAA;YAClD,MAAK;QACT;YACI,CAAC,CAAC,QAAQ,GAAG,EAAE,CAAA;YACf,MAAK;IACb,CAAC;IAED,CAAC,CAAC,QAAQ,GAAG,IAAA,kBAAU,EAAC,CAAC,CAAC,QAAQ,CAAC,CAAA;AACvC,CAAC;AAGD,6FAA6F;AAE7F,SAAS,wBAAwB,CAAC,kBAA4B,EAAE,EAAE,CAAyB;IACvF,IAAI,MAAM,GAAkC,IAAI,CAAA;IAEhD,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE,CAAC;QACjC,MAAM,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;QAEjE,IAAI,CAAC,IAAI;YAAE,SAAQ;QACnB,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW;YAAE,SAAQ;QACtE,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,CAAC,CAAC,SAAU,CAAC,KAAK;YAAE,SAAQ;QACxD,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,CAAC,CAAC,SAAU,CAAC,UAAU;YAAE,SAAQ;QACnE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,UAAU,EAAE,CAAC;YAC7D,MAAM,GAAG,MAAM,IAAI,EAAE,UAAU,EAAE,CAAC,EAAE,CAAA;YACpC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAAA;QACnD,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAA;AACjB,CAAC;AAED,SAAS,uBAAuB,CAAC,iBAA2B,EAAE,EAAE,CAAyB;IACrF,IAAI,MAAM,GAAkC,IAAI,CAAA;IAEhD,KAAK,MAAM,IAAI,IAAI,cAAc,EAAE,CAAC;QAChC,MAAM,IAAI,GAAG,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,CAAA;QAEhE,IAAI,CAAC,IAAI;YAAE,SAAQ;QACnB,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW;YAAE,SAAQ;QACtE,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,GAAG,CAAC,CAAC,SAAU,CAAC,KAAK;YAAE,SAAQ;QACvD,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,CAAC,CAAC,SAAU,CAAC,SAAS;YAAE,SAAQ;QACjE,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,SAAS,CAAC,WAAW,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;YAC3D,MAAM,GAAG,MAAM,IAAI,EAAE,SAAS,EAAE,CAAC,EAAE,CAAA;YACnC,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAA;QACjD,CAAC;IACL,CAAC;IAED,OAAO,MAAM,CAAA;AACjB,CAAC;AAED,SAAS,UAAU,CAAC,CAAyB,EAAE,UAAkB,EAAE,SAAiB;IAChF,CAAC,CAAC,UAAU,GAAG,IAAA,kBAAU,EAAC,SAAS,CAAC,CAAA;IACpC,CAAC,CAAC,SAAS,GAAG,IAAA,kBAAU,EAAC,UAAU,CAAC,CAAA;IACpC,IAAG,CAAC,CAAC,CAAC,KAAK;QAAE,OAAM;IAEnB,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAA;IAC5B,CAAC,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,SAAS,CAAA;IACpC,CAAC,CAAC,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,UAAU,CAAA;IACpC,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,KAAK,CAAC,QAAQ,CAAC,SAAS,IAAI,EAAE,CAAA;IAC5D,CAAC,CAAC,KAAK,CAAC,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC,QAAQ,CAAC,UAAU,IAAI,EAAE,CAAA;AAChE,CAAC;AAED,SAAS,SAAS,CAAC,CAAyB;IACxC,IAAG,CAAC,CAAC,SAAS,KAAK,QAAQ;QAAE,OAAM;IAEnC,oFAAoF;IACpF,MAAM,IAAI,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,IAAI,CAAC,EAC/C,aAAa,GAAG,CAAC,CAAC,KAAK,EAAE,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,CAAC,IAAI,EAAE,EACtE,mBAAmB,GAAG,aAAa,EAAE,MAAM,CAAA;IAE/C,IAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC;QAAE,OAAM;IACxC,CAAC,CAAC,UAAU,GAAG,IAAA,kBAAU,EAAC,GAAG,aAAa,IAAI,CAAC,CAAC,UAAU,EAAE,CAAC,CAAA;IAC7D,CAAC,CAAC,SAAS,GAAG,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,MAAM,GAAG,mBAAmB,GAAG,CAAC,CAAC,CAAA;AACzF,CAAC"}