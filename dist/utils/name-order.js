"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOrderByLanguage = getOrderByLanguage;
exports.getDefaultOrder = getDefaultOrder;
exports.aliasOrder = aliasOrder;
const const_1 = require("../const");
const likelihood_1 = require("./likelihood");
const FRENCH = '201', JAPANESE = '104', CHINESE = '101';
function getOrderByLanguage(language) {
    if (language && const_1.NON_ENGLISH.includes(language))
        return const_1.FAMILY_GIVEN;
    return null;
}
function getDefaultOrder(country) {
    const FIRST_NAME_1ST_COUNTRY = ['CN', 'IN', 'MG', 'LK', 'JP', 'KR', 'VN', 'KP', 'TW', 'HK', 'MO'];
    return FIRST_NAME_1ST_COUNTRY.includes(country) ? const_1.FAMILY_GIVEN : const_1.GIVEN_FAMILY;
}
function aliasOrder(R) {
    // multiple English given name
    let ethnicity = getEthnicityFromGivenName(R.words?.givenName, R.names?.givenName, R.ethnicity)
        ?? getEthnicityFromGivenName(R.words?.fallback.givenName, R.names?.givenName, R.ethnicity);
    // GivenName not in Ref DB (R.names.givenName) i.e. ethinicity = null
    // Chinese & Vietnamese
    // single word, more likely to be an English Name.
    if (!ethnicity && R.ethnicity === CHINESE && R.words?.givenName.length === 1) {
        const givenName = R.names?.givenName.find(n => n.name === R.words?.givenName[0]);
        if (!givenName) {
            ethnicity = CHINESE;
        }
    }
    return (ethnicity === FRENCH || (ethnicity === JAPANESE && R.ethnicity === parseInt(CHINESE))) ? const_1.GIVEN_FAMILY : null;
}
function getEthnicityFromGivenName(givenNameWords = [], dbNames = [], ethnicity = '') {
    const SPECIAL_NAME = ['kit', 'kat', 'kim', 'wing'];
    let result = null;
    for (const word of givenNameWords) {
        const name = dbNames.find(n => n.name === word);
        if (!name)
            continue;
        const { key } = (0, likelihood_1.likely)(name.ethnicity) ?? {};
        if (!result || key && key !== FRENCH)
            result = key;
        if (result === FRENCH
            && SPECIAL_NAME.includes(word)
            && givenNameWords.length > 1
            && ethnicity === parseInt(CHINESE))
            result = CHINESE;
    }
    return result;
}
//# sourceMappingURL=name-order.js.map