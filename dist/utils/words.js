"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.splitIntoWords = splitIntoWords;
const const_1 = require("../const");
const utils_1 = require("@perkd/utils");
function splitIntoWords(data, characterSet) {
    let { familyName = '', givenName = '' } = data;
    const words = {
        familyName: [],
        givenName: [],
        fallback: {}
    };
    if (!familyName && givenName) { // split givenName into familyName and givenName
        if (isNonEnglish(characterSet, givenName)) {
            const names = splitCharacters(givenName);
            data.familyName = names[0];
            data.givenName = names.slice(1).join('');
            words.familyName.push(names[0]);
            words.givenName.push(names.slice(1).join(''));
            familyName = names[0];
            givenName = names.slice(1).join('');
        }
        else {
            const names = (0, utils_1.splitIntoWords)(givenName);
            data.familyName = names[names.length - 1];
            data.givenName = names.slice(0, names.length - 1).join(' ');
            words.familyName.push(names[names.length - 1]);
            words.givenName.push(names.slice(0, names.length - 1).join(' '));
            familyName = names[names.length - 1];
            givenName = names.slice(0, names.length - 1).join(' ');
        }
    }
    else {
        if (familyName && isNonEnglish(characterSet, familyName)) {
            words.familyName.push(familyName);
            words.fallback.familyName = splitCharacters(familyName);
        }
        if (givenName && isNonEnglish(characterSet, givenName)) {
            words.givenName.push(givenName);
            words.fallback.givenName = splitCharacters(givenName);
        }
    }
    words.familyName = words.familyName.concat((0, utils_1.splitIntoWords)(familyName));
    words.givenName = words.givenName.concat((0, utils_1.splitIntoWords)(givenName));
    // TODO cleaner way, for chinese names
    // separateWordsKeyedInTogether('familyName', words)
    // separateWordsKeyedInTogether('givenName', words)
    return words;
}
function splitCharacters(string) {
    return string.split('');
}
function isNonEnglish(language, name) {
    return (language && const_1.NON_ENGLISH.includes(language)) || const_1.CN_JP_KR_REGEX.test(name);
}
function separateWordsKeyedInTogether(name, words) {
    const names = words[name], { fallback } = words;
    if (names.length !== 1 || names[0].length <= 3)
        return;
    const_1.SEPARATORS.some(l => {
        l.some(w => {
            const pos = names[0].indexOf(w, 2);
            if (pos > 0) {
                const name1 = names[0].substring(0, pos).replace(/[\d@()_~˙，-]/g, '').trim(), name2 = names[0].substring(pos).replace(/[\d@()_~˙，-]/g, '').trim();
                if (name1.length <= 1 || name2.length <= 1)
                    return false;
                fallback[name] = fallback[name] ?? [];
                fallback[name]?.push(name1);
                fallback[name]?.push(name2);
                return true;
            }
        });
        if (fallback[name]?.length ?? 0 > 0)
            return true;
    });
}
//# sourceMappingURL=words.js.map