type BiasThreshold = {
    bias: {
        [key: string]: Record<string, number>;
    };
    threshold: Record<string, number>;
};
/**
 * Pick the value for Property in R(esult) with highest probablity score exceeding set threshold
*/
export declare function mostLikely(scores: Record<string, number>, { bias, threshold }: BiasThreshold, property: string): string | null;
/**
 * Pick the value for Property in R(esult) with highest probablity score
 * @param  {Object} R        	collection of result objects, eg:
 *           { familyName: { ethinicity: { '101': 80, '201': 15 } }
 *             givenName: { ethnicity: { '400': 85 }, gender: { 'm': 100 } }
 *           }
 *
 * @param  {String} property  	name of property to shortlist
 * @param  {Object} bias	  	(optional) bias for property, eg.  { '102': 80, '201': 65 }
 * @return {Object}          	{ key: value} where key = value for Property, value = probability score
 */
export declare function moreLikely(R: {
    [key: string]: {
        [key: string]: Record<string, number>;
    };
}, property: string, bias: Record<string, number>): {
    key: string;
    value: number;
} | null;
/**
 * Pick the value with highest probablity score from given collection
 * @param  {Object} probabilities  	collection of { key: probability } pairs, eg.
 *                                  { '101': 80, '201': 15 }
 * @return {Object}              	{ key: probability }
 */
export declare function likely(probabilities: Record<string, number>): {
    key: string;
    value: number;
};
export {};
