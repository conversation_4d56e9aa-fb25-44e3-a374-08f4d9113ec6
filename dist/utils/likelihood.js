"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.mostLikely = mostLikely;
exports.moreLikely = moreLikely;
exports.likely = likely;
/**
 * Pick the value for Property in R(esult) with highest probablity score exceeding set threshold
*/
function mostLikely(scores, { bias, threshold }, property) {
    const most = moreLikely({ x: { [property]: scores } }, property, bias[property]);
    return most ? (most.value >= threshold[property] ? most.key : null) : null;
}
/**
 * Pick the value for Property in R(esult) with highest probablity score
 * @param  {Object} R        	collection of result objects, eg:
 *           { familyName: { ethinicity: { '101': 80, '201': 15 } }
 *             givenName: { ethnicity: { '400': 85 }, gender: { 'm': 100 } }
 *           }
 *
 * @param  {String} property  	name of property to shortlist
 * @param  {Object} bias	  	(optional) bias for property, eg.  { '102': 80, '201': 65 }
 * @return {Object}          	{ key: value} where key = value for Property, value = probability score
 */
function moreLikely(R, property, bias) {
    const scores = {};
    for (const res in R) {
        const prop = R[res][property]; // property of interest within this result object, eg.  ethincity: { }
        calculateScore(scores, prop);
    }
    calculateScore(scores, bias); // apply bias
    const most = likely(scores); // pick value with highest probability
    return most ? { key: most.key, value: most.value } : null;
}
/**
 * Pick the value with highest probablity score from given collection
 * @param  {Object} probabilities  	collection of { key: probability } pairs, eg.
 *                                  { '101': 80, '201': 15 }
 * @return {Object}              	{ key: probability }
 */
function likely(probabilities) {
    const highest = { key: '', value: -1 };
    for (const key in probabilities) {
        if (probabilities[key] > highest.value) {
            highest.key = key;
            highest.value = probabilities[key];
        }
    }
    return highest;
}
function calculateScore(scores, list) {
    if (!list)
        return;
    const FINE_TUNING = 1.2, TOTAL_SCORE = 2, MAX_PROPABILITY = 100;
    for (const key in list) {
        scores[key] = scores[key]
            ? Math.min((scores[key] + list[key]) * FINE_TUNING / TOTAL_SCORE, MAX_PROPABILITY)
            : list[key];
    }
}
//# sourceMappingURL=likelihood.js.map