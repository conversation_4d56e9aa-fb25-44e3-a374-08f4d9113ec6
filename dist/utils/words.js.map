{"version": 3, "file": "words.js", "sourceRoot": "", "sources": ["../../src/utils/words.ts"], "names": [], "mappings": ";;AAeA,wCAqDC;AApED,oCAAkE;AAClE,wCAAiE;AAcjE,SAAgB,cAAc,CAC7B,IAGC,EACD,YAA2B;IAExB,IAAI,EAAE,UAAU,GAAG,EAAE,EAAE,SAAS,GAAG,EAAE,EAAE,GAAG,IAAI,CAAC;IAC/C,MAAM,KAAK,GAAU;QACjB,UAAU,EAAE,EAAE;QACd,SAAS,EAAE,EAAE;QACnB,QAAQ,EAAE,EAAE;KACT,CAAA;IAEJ,IAAI,CAAC,UAAU,IAAI,SAAS,EAAE,CAAC,CAAC,gDAAgD;QACzE,IAAI,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,EAAE,CAAC;YACxC,MAAM,KAAK,GAAG,eAAe,CAAC,SAAS,CAAC,CAAC;YACzC,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YAC3B,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;YACxC,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;YAChC,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,CAAC;YAC9C,UAAU,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;YACtB,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;QACvC,CAAC;aACI,CAAC;YACF,MAAM,KAAK,GAAG,IAAA,sBAAgB,EAAC,SAAS,CAAC,CAAC;YAC1C,IAAI,CAAC,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAC,CAAC,CAAC,CAAC;YACxC,IAAI,CAAC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;YACzD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,MAAM,GAAC,CAAC,CAAC,CAAC,CAAC;YAC7C,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/D,UAAU,GAAG,KAAK,CAAC,KAAK,CAAC,MAAM,GAAC,CAAC,CAAC,CAAC;YACnC,SAAS,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,MAAM,GAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;QACxD,CAAC;IACR,CAAC;SAAM,CAAC;QACP,IAAG,UAAU,IAAI,YAAY,CAAC,YAAY,EAAE,UAAU,CAAC,EAAE,CAAC;YACzD,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,CAAA;YACjC,KAAK,CAAC,QAAQ,CAAC,UAAU,GAAG,eAAe,CAAC,UAAU,CAAC,CAAA;QACxD,CAAC;QAED,IAAG,SAAS,IAAI,YAAY,CAAC,YAAY,EAAE,SAAS,CAAC,EAAE,CAAC;YACvD,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,CAAA;YAC/B,KAAK,CAAC,QAAQ,CAAC,SAAS,GAAG,eAAe,CAAC,SAAS,CAAC,CAAA;QACtD,CAAC;IACF,CAAC;IAED,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,UAAU,CAAC,MAAM,CAAC,IAAA,sBAAgB,EAAC,UAAU,CAAC,CAAC,CAAA;IACxE,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,CAAC,MAAM,CAAC,IAAA,sBAAgB,EAAC,SAAS,CAAC,CAAC,CAAA;IAErE,sCAAsC;IACtC,oDAAoD;IACpD,mDAAmD;IAEnD,OAAO,KAAK,CAAA;AACb,CAAC;AAED,SAAS,eAAe,CAAC,MAAc;IACtC,OAAO,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAA;AACxB,CAAC;AAED,SAAS,YAAY,CAAC,QAAuB,EAAE,IAAY;IAC1D,OAAO,CAAC,QAAQ,IAAI,mBAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,IAAI,sBAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;AACjF,CAAC;AAED,SAAS,4BAA4B,CAAC,IAA4B,EAAE,KAAY;IAC/E,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,EAAE,GAAG,KAAK,CAAA;IAC/C,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,MAAM,IAAI,CAAC;QAAE,OAAM;IAEtD,kBAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;QACnB,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE;YACV,MAAM,GAAG,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,EAAE,CAAC,CAAC,CAAA;YAClC,IAAI,GAAG,GAAG,CAAC,EAAE,CAAC;gBACb,MAAM,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,EAC3E,KAAK,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,eAAe,EAAE,EAAE,CAAC,CAAC,IAAI,EAAE,CAAA;gBAEpE,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;oBAAE,OAAO,KAAK,CAAA;gBAExD,QAAQ,CAAC,IAAI,CAAC,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAA;gBACrC,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;gBAC3B,QAAQ,CAAC,IAAI,CAAC,EAAE,IAAI,CAAC,KAAK,CAAC,CAAA;gBAC3B,OAAO,IAAI,CAAA;YACZ,CAAC;QACF,CAAC,CAAC,CAAA;QACF,IAAI,QAAQ,CAAC,IAAI,CAAC,EAAE,MAAM,IAAI,CAAC,GAAG,CAAC;YAAE,OAAO,IAAI,CAAA;IACjD,CAAC,CAAC,CAAA;AACH,CAAC"}