"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getLanguage = getLanguage;
const utils_1 = require("@perkd/utils");
const settings_1 = require("../settings");
const const_1 = require("../const");
function getLanguage({ fullName, familyName, givenName }) {
    const language = (0, utils_1.detectLanguage)(settings_1.SUPPORTED_LANGUAGE, fullName || (familyName + ' ' + givenName));
    return language === const_1.UNDEFINED ? null : language;
}
//# sourceMappingURL=language.js.map