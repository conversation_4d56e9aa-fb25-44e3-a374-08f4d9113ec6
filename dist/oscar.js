"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Oscar = void 0;
const mongodb_1 = require("mongodb");
const const_1 = require("./const");
const settings_1 = require("./settings");
const utils_1 = require("./utils");
const { NODE_ENV, DB_HOST = NODE_ENV ? 'mongodb' : '127.0.0.1', DB_USERNAME = '', DB_PASSWORD = '', DB_AUTH = 'admin', DB_SET = '', } = process.env, URI = 'mongodb://' + (DB_USERNAME ? `${DB_USERNAME}:${DB_PASSWORD}@` : '') + `${DB_HOST}/oscar?authSource=${DB_AUTH}` + (DB_SET ? `&replicaSet=${DB_SET}` : ''), DB = 'oscar';
var Collection;
(function (Collection) {
    Collection["GIVEN_NAME"] = "GivenName";
    Collection["FAMILY_NAME"] = "FamilyName";
    Collection["NAME_ORDER"] = "NameOrder";
})(Collection || (Collection = {}));
class Oscar {
    dbClient;
    db;
    constructor() {
        this.dbClient = new mongodb_1.MongoClient(URI);
        this.db = null;
    }
    async init() {
        await this.connect();
    }
    async stop() {
        await this.dbClient.close();
    }
    async connect() {
        try {
            await this.dbClient.connect();
            this.db = this.dbClient.db(DB);
            // console.log(`Successfully connected to db ${this.db.databaseName}`)
        }
        catch (err) {
            console.error(`we encountered ${err}`);
        }
    }
    async clean(data, options = {}) {
        const { locale, name, gender = null } = data, { country = '', languages = [] } = locale ?? {}, language = languages?.[0] ?? '', displayAs = name?.displayAs ?? null, opt = {
            ...settings_1.DEFAULT_SETTING,
            ...options
        };
        (0, utils_1.sanitize)(data);
        const R = {
            language: (language && settings_1.SUPPORTE_LANGUAGED_MAPPING[language.substring(0, 2)])
                ? settings_1.SUPPORTE_LANGUAGED_MAPPING[language.substring(0, 2)]
                : (0, utils_1.getLanguage)(data),
            characterSet: (0, utils_1.getLanguage)(data),
            country,
            customer: {
                displayAs,
                gender,
            },
            bias: opt.bias,
            threshold: opt.threshold,
            words: (0, utils_1.splitIntoWords)(data, (0, utils_1.getLanguage)(data))
        };
        R.names = await this.getNames(R.words?.familyName.concat(R.words?.givenName, R.words?.fallback.familyName ?? [], R.words?.fallback.givenName ?? []) ?? []);
        (0, utils_1.setEthnicity)(data, R);
        (0, utils_1.setGender)(data, R);
        (0, utils_1.setNamesProbabilityScore)(data, R);
        await this.setNameOrder(name?.order ?? '', R);
        (0, utils_1.setFullName)(R);
        return R;
    }
    async setNameOrder(order, R) {
        if (order) {
            R.name = { order };
            return;
        }
        const { familyName, givenName, characterSet, ethnicity, customer, language, country = '' } = R;
        if (!familyName && !givenName) {
            R.name = { order: null };
            return;
        }
        if (!familyName) {
            R.name = { order: const_1.GIVEN_FAMILY };
            return;
        }
        if (!givenName || const_1.NON_ENGLISH.includes(characterSet ?? '')) {
            R.name = { order: const_1.FAMILY_GIVEN };
            return;
        }
        if (ethnicity) {
            const nameOrder = await this.db?.collection(Collection.NAME_ORDER).findOne({ _id: ethnicity }), order = nameOrder?.order;
            R.name = {
                order: (0, utils_1.getOrderByLanguage)(characterSet)
                    ?? (0, utils_1.aliasOrder)(R)
                    ?? (0, utils_1.getOrderByLanguage)(language)
                    ?? order
                    ?? R.bias?.nameOrder
                    ?? (0, utils_1.getDefaultOrder)(country)
            };
            return;
        }
        R.name = { order: (0, utils_1.aliasOrder)(R) ?? customer?.displayAs ?? (0, utils_1.getDefaultOrder)(country) };
        console.log('NameOrderNotMatch', { R });
    }
    async getNames(names) {
        const filter = { name: { $in: names } }, options = { projection: { _id: false } };
        const givenNameQuery = this.db?.collection(Collection.GIVEN_NAME).find(filter, options), familyNameQuery = this.db?.collection(Collection.FAMILY_NAME).find(filter, options);
        const [givenName, familyName] = await Promise.all([
            givenNameQuery?.toArray(),
            familyNameQuery?.toArray()
        ]);
        return { givenName, familyName };
    }
}
exports.Oscar = Oscar;
//# sourceMappingURL=oscar.js.map