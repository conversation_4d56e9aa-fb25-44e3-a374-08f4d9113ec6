import { CleanseResult, Names } from './types';
export declare class Oscar {
    private dbClient;
    private db;
    constructor();
    init(): Promise<void>;
    stop(): Promise<void>;
    connect(): Promise<void>;
    clean(data: {
        firstName?: string;
        givenName?: string;
        name?: {
            order?: string | null;
            displayAs?: string | null;
        };
        gender?: string | null;
        locale?: {
            country?: string;
            languages?: string[];
        };
        ethnicity?: number | null;
    }, options?: {}): Promise<Partial<CleanseResult>>;
    setNameOrder(order: string, R: Partial<CleanseResult>): Promise<void>;
    getNames(names: string[]): Promise<Names>;
}
