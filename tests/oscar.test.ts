import assert from 'node:assert'
import { describe, it } from 'node:test'
import { <PERSON>, CleanseR<PERSON>ult } from '../src'

const NAMES = ['carl', 'barker', 'huam', 'maricel', 'maritza']

describe('<PERSON>', () => {
	it('should connect to mongo', async () => {
		const oscar = new Oscar()
        await oscar.init()
		await oscar.stop()
    })

	it('should get names from database', async () => {
		const oscar = new <PERSON>()
		await oscar.init()

		const names = await oscar.getNames(NAMES)

		await oscar.stop()
	})

	it('should clean empty profile', async () => {
		const oscar = new Oscar()
		await oscar.init()

		const profile = {},
			cleanse = await oscar.clean(profile),
			actual = pickResult(cleanse),
			expected = {
				familyName: '',
				givenName: '',
				fullName: '',
				name: {
					order: null,
				},
				gender: null,
				ethnicity: null,
			}

		assert.deepEqual(actual, expected)
		await oscar.stop()
	})

	it('should clean profile with order', async () => {
		const oscar = new Oscar()
		await oscar.init()

		const profile = {
				familyName: 'John',
				givenName: 'Doe',
				name: {
					order: 'familygiven',
				},
			},
			cleanse = await oscar.clean(profile),
			actual = pickResult(cleanse),
			expected = {
				familyName: 'Doe',
				givenName: 'John',
				fullName: 'Doe John',
				name: {
					order: 'familygiven',
				},
				gender: 'm',
				ethnicity: 201,
			}

		assert.deepEqual(actual, expected)
		await oscar.stop()
	})

	it('should clean profile with no order', async () => {
		const oscar = new Oscar()
		await oscar.init()

		const profile = {
				familyName: 'John',
				givenName: 'Doe',
				name: {
					order: null,
				},
			},
			cleanse = await oscar.clean(profile),
			actual = pickResult(cleanse),
			expected = {
				familyName: 'Doe',
				givenName: 'John',
				fullName: 'John Doe',
				name: {
					order: 'givenfamily',
				},
				gender: 'm',
				ethnicity: 201,
			}

		assert.deepEqual(actual, expected)
		await oscar.stop()
	})

	it('should clean profile and swap names (with order)', async () => {
		const oscar = new Oscar()
		await oscar.init()

		const profile = {
				familyName: 'John',
				givenName: 'Doe',
				name: {
					order: 'familygiven',
				},
			},
			cleanse = await oscar.clean(profile),
			actual = pickResult(cleanse),
			expected = {
				familyName: 'Doe',
				givenName: 'John',
				fullName: 'Doe John',
				name: {
					order: 'familygiven',
				},
				gender: 'm',
				ethnicity: 201,
			}

		assert.deepEqual(actual, expected)
		await oscar.stop()
	})

	it('should clean profile with duplicated family & given name', async () => {
		const oscar = new Oscar()
		await oscar.init()

		const profile = {
			gender: 'm',
			givenName: '张',
			familyName: '张',
			dateList: [
				{
					name: 'birth',
					day: 10,
					year: 1999,
					month: 4
				}
			]
			},
			cleanse = await oscar.clean(profile),
			actual = pickResult(cleanse),
			expected = {
				familyName: '张',
				givenName: '',
				fullName: '张',
				name: {
					order: 'familygiven',
				},
				gender: 'm',
				ethnicity: 101,
			}

		assert.deepEqual(actual, expected)
		await oscar.stop()
	})

	it('should clean profile correctly', async () => {
		const oscar = new Oscar()
		await oscar.init()

		const profile = {
			gender: 'm',
			givenName: '李佳麒',
			familyName: '李',
			dateList: [
				{
					name: 'birth',
					day: 24,
					year: 2005,
					month: 4
				}
			]
			},
			cleanse = await oscar.clean(profile),
			actual = pickResult(cleanse),
			expected = {
				familyName: '李',
				givenName: '佳麒',
				fullName: '李佳麒',
				name: {
					order: 'familygiven',
				},
				gender: 'm',
				ethnicity: 101,
			}

		assert.deepEqual(actual, expected)
		await oscar.stop()
	})
})

function pickResult(R: Partial<CleanseResult>) {
	return {
		familyName: R.familyName,
		givenName: R.givenName,
		fullName: R.fullName,
		name: {
			order: R.name?.order ?? null,
		},
		gender: R.gender,
		ethnicity: R.ethnicity,
	}
}
